const mongoose = require('mongoose');
const Course = require('./src/models/Course');
require('dotenv').config();

// MongoDB connection
const connectDB = async () => {
  try {
    await mongoose.connect('mongodb+srv://mstorsulam786:<EMAIL>/zero_koin');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Display courses with details
const showCourses = async () => {
  try {
    const courses = await Course.find().populate('uploadedBy', 'email').sort({ createdAt: -1 });
    
    if (courses.length === 0) {
      console.log('📚 No courses found in the database');
      return;
    }

    console.log(`\n📚 Found ${courses.length} course(s):\n`);
    console.log('='.repeat(80));

    courses.forEach((course, index) => {
      console.log(`\n${index + 1}. Course Details:`);
      console.log(`   📖 Name: ${course.courseName}`);
      console.log(`   🆔 ID: ${course._id}`);
      console.log(`   📄 Pages: ${course.pages.length} page(s)`);
      console.log(`   ⏰ Time: ${course.time !== null && course.time !== undefined && course.time !== '' ? course.time : 'Not specified'} (Raw: ${JSON.stringify(course.time)})`);
      console.log(`   ✅ Active: ${course.isActive ? 'Yes' : 'No'}`);
      console.log(`   👤 Uploaded by: ${course.uploadedBy?.email || 'Unknown'}`);
      console.log(`   📅 Created: ${course.createdAt.toLocaleDateString()}`);
      console.log(`   🔄 Updated: ${course.updatedAt.toLocaleDateString()}`);
      console.log('-'.repeat(50));
    });

  } catch (error) {
    console.error('❌ Error fetching courses:', error);
  }
};

// Main execution
const main = async () => {
  await connectDB();
  await showCourses();
  mongoose.connection.close();
  console.log('\n✅ Database connection closed');
};

main();
